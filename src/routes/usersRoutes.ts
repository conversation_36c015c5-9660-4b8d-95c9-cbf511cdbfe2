import express from 'express';

import {
  deleteUser,
  getAdmins,
  getLoggedInUser,
  getManagers,
  getSuperAdmins,
  getUser,
  getUserCars,
  getUsers,
  updateUser,
  updateUserRole,
  verifyOTP
} from '../controllers/users_controller.js';
import { authorize, restrictToRoles } from '../middleware/permission-middleware.js';

const router = express.Router();

// General user routes
router.get('/', authorize, restrictToRoles(['admin']), getUsers);
router.get('/me', authorize, restrictToRoles(['manager', 'admin']), getLoggedInUser);

// Role-specific user lists
router.get('/admins', authorize, restrictToRoles(['super-admin']), getAdmins);
router.get('/super-admins', authorize, restrictToRoles(['super-admin']), getSuperAdmins);
router.get('/managers', authorize, restrictToRoles(['admin']), getManagers);

// Individual user routes
router.get('/:id', authorize, restrictToRoles(['admin', 'manager']), getUser);
router.put('/:id', authorize, restrictToRoles(['admin', 'manager']), updateUser);
router.patch('/:id', authorize, restrictToRoles(['super-admin']), updateUserRole);
router.delete('/:id', authorize, restrictToRoles(['admin']), deleteUser);

// User sub-resources
router.get('/:id/cars', authorize, restrictToRoles(['admin']), getUserCars);
router.put('/:id/verify/:code', authorize, restrictToRoles(['admin', 'user']), verifyOTP);

export default router;
